import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Check for Firebase Auth session cookie
  const firebaseAuthCookie = request.cookies.get('firebase:authUser:AIzaSyD0S1kQmOplY8z3wWYmmvX1DfwYNaxW29o:[DEFAULT]')?.value;
  const currentUser = request.cookies.get('currentUser')?.value;

  const isAuthenticated = !!firebaseAuthCookie || !!currentUser;

  // Check if the request is for dashboard and there's no authentication
  if (request.nextUrl.pathname.startsWith('/dashboard') && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If user is already logged in, redirect from login/signup to dashboard
  if ((request.nextUrl.pathname.startsWith('/login') ||
       request.nextUrl.pathname === '/') &&
      isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/login'],
};