@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 218 86% 25%;
    --primary-foreground: 218 10% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 204 65% 45%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 218 86% 25%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 222 47% 11%;
    --foreground: 0 0% 98%;
    --card: 222 47% 13%;
    --card-foreground: 0 0% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 0 0% 98%;
    --primary: 218 86% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 222 47% 18%;
    --secondary-foreground: 0 0% 98%;
    --muted: 222 47% 18%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 204 65% 55%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 222 47% 25%;
    --input: 222 47% 18%;
    --ring: 218 86% 60%;
    --chart-1: 220 70% 60%;
    --chart-2: 160 60% 55%;
    --chart-3: 30 80% 65%;
    --chart-4: 280 65% 70%;
    --chart-5: 340 75% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }

  p, span, div, button, input, textarea {
    @apply font-sans;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.document-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.document-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Equal width tabs */
.equal-tabs [role="tablist"] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
}

.equal-tabs [role="tab"] {
  flex: 1;
  width: 100%;
}