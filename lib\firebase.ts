import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';


const firebaseConfig = {
  apiKey: "AIzaSyD0S1kQmOplY8z3wWYmmvX1DfwYNaxW29o",
  authDomain: "gurudev-cee19.firebaseapp.com",
  databaseURL: "https://gurudev-cee19-default-rtdb.firebaseio.com",
  projectId: "gurudev-cee19",
  storageBucket: "gurudev-cee19.firebasestorage.app",
  messagingSenderId: "45932894435",
  appId: "1:45932894435:web:9dd77675eaf4eb6eaccdd6"
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);

export { app, auth, db, storage };