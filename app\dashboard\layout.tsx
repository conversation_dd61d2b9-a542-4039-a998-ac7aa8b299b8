"use client";

import { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ModeToggle } from "@/components/mode-toggle";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/lib/auth-provider";
import { Skeleton } from "@/components/ui/skeleton";
import { NewspaperIcon } from "@/components/icons/newspaper-icon";
import { UserIcon, LogOut, FileText, BarChart3 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { userData, loading, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("summarization");

  useEffect(() => {
    if (pathname.includes("data")) {
      setActiveTab("data");
    } else {
      setActiveTab("summarization");
    }
  }, [pathname]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    router.push(`/dashboard/${value === "summarization" ? "" : value}`);
  };

  const handleSignOut = async () => {
    await signOut();
    router.push("/login");
  };

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <header className="sticky top-0 z-10 border-b bg-background/95 backdrop-blur">
          <div className="container flex h-16 items-center justify-between px-4">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-6 w-24" />
            </div>
            <div className="flex items-center gap-4">
              <Skeleton className="h-9 w-9 rounded-full" />
              <Skeleton className="h-9 w-9 rounded-full" />
            </div>
          </div>
        </header>
        <div className="container flex-1 items-start px-4 py-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Skeleton className="h-[200px] rounded-lg" />
            <Skeleton className="h-[200px] rounded-lg" />
            <Skeleton className="h-[200px] rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-10 border-b bg-background/95 backdrop-blur">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <NewspaperIcon className="h-6 w-6 text-primary" />
            <span className="font-serif text-xl font-bold">NewsAI</span>
          </div>
          <div className="flex items-center gap-4">
            <ModeToggle />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <UserIcon className="h-5 w-5" />
                  <span className="sr-only">User menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {userData?.email || "User"}
                </DropdownMenuLabel>
                <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                  {userData?.role === "superadmin" ? "Super Admin" : "User"}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="container px-4 py-2">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full equal-tabs">
            <TabsList className="grid w-full grid-cols-2 gap-0">
              <TabsTrigger value="summarization" className="flex items-center justify-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Summarization</span>
                <span className="inline sm:hidden">Summary</span>
              </TabsTrigger>
              <TabsTrigger value="data" className="flex items-center justify-center gap-2">
                <BarChart3 className="h-4 w-4" />
                <span className="hidden sm:inline">Data & History</span>
                <span className="inline sm:hidden">Data</span>
              </TabsTrigger>

            </TabsList>
          </Tabs>
        </div>
      </header>
      <main className="flex-1">{children}</main>
    </div>
  );
}