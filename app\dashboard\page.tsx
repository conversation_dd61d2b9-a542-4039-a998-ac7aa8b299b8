"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDropzone } from "react-dropzone";
import { useAuth } from "@/lib/auth-provider";
import { storage, db } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { collection, addDoc } from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { summarizeText, processPdfWithVision, DEFAULT_LEAD_GENERATION_PROMPT } from "@/lib/gemini";
import { Document } from "@/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { FileTextIcon, RefreshCw, FileType, Newspaper } from "lucide-react";

const uploadSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().optional(),
  prompt: z.string().default(DEFAULT_LEAD_GENERATION_PROMPT),
  isPdfMode: z.boolean().default(false),
});

type UploadFormValues = z.infer<typeof uploadSchema> & {
  isPdfMode: boolean;
};

export default function DashboardPage() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { userData } = useAuth();
  const { toast } = useToast();
  const router = useRouter();

  const form = useForm<UploadFormValues>({
    resolver: zodResolver(uploadSchema),
    defaultValues: {
      title: "",
      content: "",
      prompt: DEFAULT_LEAD_GENERATION_PROMPT,
      isPdfMode: false,
    },
  });

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxFiles: 10, // Allow up to 10 files
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length > 0) {
        setUploadedFiles(acceptedFiles);

        // For simplicity, we'll just use the first file name as the title if no title is set
        if (!form.getValues("title")) {
          form.setValue("title", acceptedFiles[0].name.split('.')[0]);
        }

        // For plain text files, we can read the content of the first file
        if (acceptedFiles[0].type === 'text/plain') {
          const reader = new FileReader();
          reader.onload = (e) => {
            const content = e.target?.result as string;
            form.setValue("content", content);
          };
          reader.readAsText(acceptedFiles[0]);
        } else if (acceptedFiles.length > 1) {
          toast({
            title: `${acceptedFiles.length} files uploaded`,
            description: "Multiple files will be processed in sequence.",
          });
        } else {
          // For other file types
          toast({
            title: "File uploaded",
            description: "PDF files will be analyzed automatically.",
          });
        }
      }
    },
  });

  // Helper function to process a single file
  const processFile = async (file: File, data: UploadFormValues, userData: any) => {
    // Create document reference
    const documentId = uuidv4();
    let fileUrl = "";
    let summary = "";

    // Upload file to Firebase Storage
    const storageRef = ref(storage, `documents/${userData.uid}/${documentId}/${file.name}`);
    const snapshot = await uploadBytes(storageRef, file);
    fileUrl = await getDownloadURL(snapshot.ref);

    // If it's a PDF file, process it with Gemini Vision
    if (file.type === 'application/pdf' || data.isPdfMode) {
      // Convert file to base64
      const fileReader = new FileReader();
      const fileBase64Promise = new Promise<string>((resolve) => {
        fileReader.onload = (e) => {
          const base64 = (e.target?.result as string)?.split(',')[1] || '';
          resolve(base64);
        };
      });
      fileReader.readAsDataURL(file);
      const base64Data = await fileBase64Promise;

      // Process PDF with Gemini Vision
      summary = await processPdfWithVision(base64Data, data.prompt);
    } else if (file.type === 'text/plain') {
      // For text files, read the content and process with regular Gemini
      const reader = new FileReader();
      const textContentPromise = new Promise<string>((resolve) => {
        reader.onload = (e) => {
          const content = e.target?.result as string;
          resolve(content);
        };
      });
      reader.readAsText(file);
      const textContent = await textContentPromise;

      // Process with regular Gemini
      summary = await summarizeText(textContent, data.prompt);
    } else {
      // For other file types, use the provided content or a placeholder
      summary = await summarizeText(data.content || `Content from ${file.name}`, data.prompt);
    }

    // Process the summary and save to Firestore
    await processSummaryAndSave(summary, {
      documentId,
      userData,
      data,
      fileUrl,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size
    });

    return documentId;
  };

  // Helper function to process text content
  const processTextContent = async (data: UploadFormValues, userData: any) => {
    // Create document reference
    const documentId = uuidv4();

    // Process with regular Gemini
    const summary = await summarizeText(data.content || "", data.prompt);

    // Process the summary and save to Firestore
    await processSummaryAndSave(summary, {
      documentId,
      userData,
      data,
      fileUrl: "",
      fileName: "Manual entry",
      fileType: "text/plain",
      fileSize: data.content?.length || 0
    });

    return documentId;
  };

  // Helper function to process summary and save to Firestore
  const processSummaryAndSave = async (summary: string, options: {
    documentId: string;
    userData: any;
    data: UploadFormValues;
    fileUrl: string;
    fileName: string;
    fileType: string;
    fileSize: number;
  }) => {
    const { documentId, userData, data, fileUrl, fileName, fileType, fileSize } = options;

    // Parse summary to extract title and content
    const summaryLines = summary.split('\n');
    let summaryTitle = "";
    let summaryContent = "";
    let extractedKeywords: string[] = [];

    // Simple parsing (can be improved for production)
    if (summaryLines[0].startsWith("TITLE:")) {
      summaryTitle = summaryLines[0].replace("TITLE:", "").trim();

      // Find keywords section
      const keywordsIndex = summaryLines.findIndex(line => line.startsWith("KEYWORDS:"));

      if (keywordsIndex > 0) {
        summaryContent = summaryLines.slice(2, keywordsIndex).join('\n').trim();
        const keywordsLine = summaryLines[keywordsIndex].replace("KEYWORDS:", "").trim();
        extractedKeywords = keywordsLine.split(',').map(k => k.trim());
      } else {
        // If no keywords section found, use everything after title
        summaryContent = summaryLines.slice(2).join('\n').trim();
      }
    } else {
      // If no title format, use everything
      summaryTitle = data.title;
      summaryContent = summary;
    }

    // Extract keywords from the content if none were found
    if (extractedKeywords.length === 0 && data.content) {
      // Use a simple extraction method - this could be improved
      const words = data.content.toLowerCase().split(/\W+/);
      const wordFrequency: Record<string, number> = {};

      // Count word frequency
      words.forEach(word => {
        if (word.length > 3 && !['this', 'that', 'with', 'from', 'have', 'were', 'they', 'their'].includes(word)) {
          wordFrequency[word] = (wordFrequency[word] || 0) + 1;
        }
      });

      // Get top 5 most frequent words as keywords
      extractedKeywords = Object.entries(wordFrequency)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([word]) => word);
    }

    // If still no keywords, add some default ones
    if (extractedKeywords.length === 0) {
      extractedKeywords = ["business", "opportunity", "lead", "power", "infrastructure"];
    }

    // Create timestamp for consistent date values
    const timestamp = Date.now();

    // Save document metadata to Firestore
    const docData: Document = {
      id: documentId,
      userId: userData.uid,
      title: data.title,
      originalText: data.content || "PDF document - content extracted via AI",
      fileUrl: fileUrl,
      fileName: fileName,
      fileType: fileType,
      fileSize: fileSize,
      uploadDate: timestamp,
      keywords: extractedKeywords,
      status: 'completed',
    };

    console.log("Saving document to Firestore:", docData);
    const docRef = await addDoc(collection(db, 'documents'), docData);
    console.log("Document saved with ID:", docRef.id);

    // Save summary to Firestore
    const summaryData = {
      id: uuidv4(),
      documentId: documentId,
      userId: userData.uid,
      title: summaryTitle || data.title, // Fallback to document title if no summary title
      content: summaryContent,
      keywords: extractedKeywords,
      createdAt: timestamp, // Use the same timestamp as the document
    };

    console.log("Saving summary to Firestore:", summaryData);
    const summaryRef = await addDoc(collection(db, 'summaries'), summaryData);
    console.log("Summary saved with ID:", summaryRef.id);
  };

  const onSubmit = async (data: UploadFormValues) => {
    if (!userData) {
      toast({
        title: "Authentication error",
        description: "Please log in to continue",
        variant: "destructive",
      });
      return;
    }

    // Validate that we have either content or uploaded files
    if (!data.content && uploadedFiles.length === 0) {
      toast({
        title: "Missing content",
        description: "Please upload PDF files or enter text content",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // If we have text content but no files, process it as a single document
      if (data.content && uploadedFiles.length === 0) {
        await processTextContent(data, userData);
      }
      // If we have files, process each one
      else if (uploadedFiles.length > 0) {
        // Process each file sequentially
        for (let i = 0; i < uploadedFiles.length; i++) {
          const file = uploadedFiles[i];
          const fileTitle = i === 0 ? data.title : `${data.title} - ${i + 1}`;

          toast({
            title: `Processing file ${i + 1} of ${uploadedFiles.length}`,
            description: file.name,
          });

          try {
            await processFile(file, {
              ...data,
              title: fileTitle
            }, userData);
          } catch (fileError: any) {
            console.error(`Error processing file ${file.name}:`, fileError);
            toast({
              title: `Error processing ${file.name}`,
              description: fileError.message || "Failed to process file",
              variant: "destructive",
            });
            // Continue with next file
            continue;
          }
        }
      }

      // Success message
      toast({
        title: uploadedFiles.length > 1 ?
          `Processed ${uploadedFiles.length} files successfully` :
          "Success!",
        description: "Your document(s) have been analyzed successfully",
      });

      // Wait a moment to ensure data is saved before redirecting
      setTimeout(() => {
        // Redirect to data page to view the summary
        router.push("/dashboard/data");
      }, 1000);

    } catch (error: any) {
      console.error("Error processing document:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to process document",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6 max-w-5xl">
      <div className="mb-8">
        <h1 className="text-3xl font-serif font-bold mb-2">Document Processing</h1>
        <p className="text-muted-foreground">
          Upload multiple documents or paste content to generate targeted summaries and extract business leads.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileType className="h-5 w-5" />
                Upload Document
              </CardTitle>
              <CardDescription>
                Upload multiple PDFs or documents for lead generation and analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="isPdfMode"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 mb-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">PDF Analysis Mode</FormLabel>
                      <FormDescription>
                        Enable for newspaper PDFs to extract business leads
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 cursor-pointer text-center transition-colors ${
                  isDragActive ? "border-primary bg-primary/5" : "border-muted"
                }`}
              >
                <input {...getInputProps()} />
                <div className="flex flex-col items-center gap-2">
                  <Newspaper className="h-10 w-10 text-muted-foreground" />
                  {uploadedFiles.length > 0 ? (
                    <div className="w-full">
                      <p className="font-medium">{uploadedFiles.length} file(s) selected</p>
                      <div className="max-h-[100px] overflow-y-auto mt-2 text-left">
                        {uploadedFiles.map((file, index) => (
                          <div key={index} className="text-sm flex justify-between items-center py-1 border-b border-muted">
                            <span className="truncate max-w-[200px]">{file.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {(file.size / 1024).toFixed(2)} KB
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="font-medium">Drag & drop newspaper PDFs here, or click to select</p>
                      <p className="text-sm text-muted-foreground">
                        Supports multiple PDF files (max 10 files, 10MB each)
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Enter the document details and keywords for targeted summarization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter document title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Content</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={form.watch('isPdfMode') ?
                          "Optional: Add any additional context or notes" :
                          "Enter document content here..."}
                        className="min-h-[200px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {form.watch('isPdfMode') ?
                        "When using PDF mode, content is optional and will be extracted from the PDF" :
                        "Enter the document content for analysis and summarization"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>AI Prompt</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={form.watch('isPdfMode') ?
                          "Lead generation prompt for UPS/stabilizer industry..." :
                          "Summarize this article and extract the key points..."}
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {form.watch('isPdfMode') ?
                        "This prompt will be used to extract business leads from the PDF" :
                        "Enter a prompt for the AI to guide the summarization process"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isProcessing} className="w-full">
                {isProcessing ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FileTextIcon className="mr-2 h-4 w-4" />
                    {form.watch('isPdfMode') ? "Extract Business Leads" : "Generate Summary"}
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}