export type UserRole = 'user' | 'superadmin';

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  role: UserRole;
  createdAt: number;
  lastLogin?: number;
}

export interface Document {
  id: string;
  userId: string;
  title: string;
  originalText: string;
  fileUrl?: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: number;
  keywords: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface Summary {
  id: string;
  documentId: string;
  userId: string;
  title: string;
  content: string;
  keywords: string[];
  createdAt: number;
  relevanceScore?: number;
}