"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut
} from 'firebase/auth';

import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from './firebase';
import { User, UserRole } from '@/types';

// Use any type for Firebase User to avoid type issues
type FirebaseUser = any;

interface AuthContextType {
  currentUser: FirebaseUser | null;
  userData: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, role?: UserRole) => Promise<void>;
  signOut: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  userData: null,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  isAdmin: false,
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user: any) => {
      setCurrentUser(user as FirebaseUser | null);
      if (user) {
        try {
          // Try to get user data from Firestore
          const userDocRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userDocRef);

          if (userDoc.exists()) {
            setUserData(userDoc.data() as User);
          } else {
            // If no Firestore document exists, check localStorage
            const localUserData = localStorage.getItem('currentUser');
            if (localUserData) {
              const parsedUserData = JSON.parse(localUserData);
              if (parsedUserData.uid === user.uid) {
                setUserData(parsedUserData as User);

                // Try to create the user document in Firestore again
                try {
                  await setDoc(userDocRef, {
                    ...parsedUserData,
                    lastLogin: Date.now(),
                  });
                } catch (firestoreError: any) {
                  console.error('Error creating user document in Firestore:', firestoreError);
                }
              }
            }
          }
        } catch (error: any) {
          console.error('Error fetching user data:', error);
          // Check localStorage as fallback
          const localUserData = localStorage.getItem('currentUser');
          if (localUserData) {
            const parsedUserData = JSON.parse(localUserData);
            if (parsedUserData.uid === user.uid) {
              setUserData(parsedUserData as User);
            }
          }
        }
      } else {
        setUserData(null);
        // Clear localStorage when user signs out
        localStorage.removeItem('currentUser');
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user: any = userCredential.user;

      try {
        // Try to update last login time in Firestore
        if (user) {
          const userDocRef = doc(db, 'users', user.uid);
          await setDoc(userDocRef, { lastLogin: Date.now() }, { merge: true });

          // Also check if we need to create the user document
          const userDoc = await getDoc(userDocRef);
          if (!userDoc.exists()) {
            // Create a basic user document if it doesn't exist
            await setDoc(userDocRef, {
              uid: user.uid,
              email: user.email,
              role: 'user',
              createdAt: Date.now(),
              lastLogin: Date.now(),
            });
          }
        }
      } catch (firestoreError: any) {
        console.error('Error updating Firestore on sign in:', firestoreError);
        // Continue even if Firestore update fails
      }

      // Update localStorage as fallback
      if (user && typeof window !== 'undefined') {
        const userData = {
          uid: user.uid,
          email: user.email,
          role: 'user', // Default role
          createdAt: Date.now(),
          lastLogin: Date.now(),
        };
        localStorage.setItem('currentUser', JSON.stringify(userData));
      }
    } catch (error: any) {
      console.error('Error signing in:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, role: UserRole = 'user') => {
    try {
      const { user }: { user: any } = await createUserWithEmailAndPassword(auth, email, password);

      try {
        // Create user document in Firestore
        const userDocRef = doc(db, 'users', user.uid);
        await setDoc(userDocRef, {
          uid: user.uid,
          email: user.email,
          role,
          createdAt: Date.now(),
          lastLogin: Date.now(),
        });
      } catch (firestoreError: any) {
        console.error('Error creating user document in Firestore:', firestoreError);
        // Continue even if Firestore document creation fails
        // This allows users to still log in even if Firestore rules are restrictive
        // You should deploy proper Firestore rules using the firestore.rules file
      }

      // Store user info in localStorage as a fallback
      if (typeof window !== 'undefined') {
        localStorage.setItem('currentUser', JSON.stringify({
          uid: user.uid,
          email: user.email,
          role: role,
          createdAt: Date.now(),
        }));
      }
    } catch (error: any) {
      console.error('Error signing up:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
    } catch (error: any) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    currentUser,
    userData,
    loading,
    signIn,
    signUp,
    signOut,
    isAdmin: userData?.role === 'superadmin',
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};