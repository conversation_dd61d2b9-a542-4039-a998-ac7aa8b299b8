/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { unoptimized: true },
  webpack: (config, { isServer }) => {
    // Add a resolve alias for undici to use an older version that doesn't use private class fields
    config.resolve.alias = {
      ...config.resolve.alias,
      undici: false, // This tells webpack to ignore undici and use the Node.js built-in fetch instead
    };

    return config;
  },
};

module.exports = nextConfig;