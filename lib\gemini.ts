import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";

let genAI: GoogleGenerativeAI | null = null;

export function initGemini() {
  if (!genAI && process.env.NEXT_PUBLIC_GEMINI_API_KEY) {
    genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY);
  }
  return genAI;
}

// Default lead generation prompt for UPS/stabilizer industry
export const DEFAULT_LEAD_GENERATION_PROMPT = `
🎯 PURPOSE
You are a lead-generation expert assistant for a company that manufactures UPS systems, Servo Stabilizers, Isolation Transformers, and Voltage Regulators.

You are given PDF files of epaper-style newspapers (e.g., Economic Times). These may be scanned or image-based PDFs.

Your job is to read the entire PDF using your vision capabilities, extract real business opportunities, and return structured results.

🔍 CAPABILITIES REQUIRED
✅ Use your own Vision model to read image-based PDF content (scanned newspaper layouts, photos with text, etc.)
✅ Use web search to find missing contact details (company websites, phone numbers, LinkedIn pages)
✅ Cover ALL pages of the PDF
✅ Output in a clean, sectioned, structured format

✅ WHAT TO EXTRACT AND RETURN:
1. 🏢 New Business Leads
Find any article announcing:

New factories / industrial plants

New schools, hospitals, colleges, malls, hotels, office complexes

Corporate expansions, infrastructure projects

Data centers, IT parks, SEZs

For each opportunity:

🏷 Name of company / organization

🏗 Project type (hospital, school, factory, etc.)

📍 Location (city/state)

🗓 Timeline (if mentioned)

📞 Contact info from PDF (if any)

🌐 If contact info NOT in PDF → Search the web and find:

Website

Phone

Email

LinkedIn of company/decision-maker

⚡ Use-Case Justification: "Why they may need UPS/Stabilizer"

2. ⚙ Stabilizer/UPS Use-Case Detection
For each lead above, clearly state:

How UPS or stabilizers would help

Link it to the sector (e.g., "Pharma plants need constant clean power to avoid batch failures")

3. 📈 Market Trends
Scan full newspaper and summarize:

📊 Industries on the rise (EV, Healthcare, Manufacturing, IT)

📉 Declining sectors (e.g., auto exports, textile closures)

🧾 Policy/govt news affecting infrastructure or energy demand

⚡ Mentions of energy issues, load shedding, carbon targets, solar/hybrid transitions

4. 🔍 Contact Discovery (Web + PDF)
For any entity mentioned in the articles:

Extract available phone numbers, emails, websites from the PDF

If not available, then:

Use real-time web search

Find company profile, contact page, LinkedIn profile of key people (e.g., VP Operations, MD)

5. 📄 Page Number References
Always include:

Page number where article was found

Source headline (if visible)


🆕 New Section to Include:
For every lead you extract, start with this new mandatory section:

📰 News Context Summary
Give a 2–4 line summary of the actual article content that explains:

What exactly is happening

Who's involved

Why it matters

Key triggers (investment amount, MOU signed, approvals, etc.)
`;

export async function summarizeText(text: string, customPrompt: string) {
  try {
    const genAI = initGemini();

    if (!genAI) {
      throw new Error("Gemini API not initialized");
    }

    // Use the model with increased quota
    const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

    const generationConfig = {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 1024,
    };

    const safetySettings = [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ];

    // Combine the custom prompt with formatting instructions
    const prompt = `
    ${customPrompt}

    Article text:
    ${text}

    Format the response as:

    TITLE: [Summary Title]

    [Summary paragraphs]

    KEYWORDS: [List of important keywords or topics found in the text]
    `;

    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig,
      safetySettings,
    });

    const response = result.response;
    return response.text();

  } catch (error: any) {
    console.error('Error summarizing text with Gemini:', error);

    // Check if it's a model-related error
    if (error.message && error.message.includes('model')) {
      // Try with a different model as fallback
      try {
        console.log('Attempting with fallback model gemini-2.5-flash...');
        const fallbackModel = genAI?.getGenerativeModel({ model: "gemini-2.5-flash" });

        if (!fallbackModel) {
          throw new Error("Fallback model initialization failed");
        }

        // Define fallback configuration
        const fallbackGenerationConfig = {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        };

        const fallbackSafetySettings = [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ];

        // Combine the custom prompt with formatting instructions
        const fallbackPrompt = `
        ${customPrompt}

        Article text:
        ${text}

        Format the response as:

        TITLE: [Summary Title]

        [Summary paragraphs]

        KEYWORDS: [List of important keywords or topics found in the text]
        `;

        const fallbackResult = await fallbackModel.generateContent({
          contents: [{ role: "user", parts: [{ text: fallbackPrompt }] }],
          generationConfig: fallbackGenerationConfig,
          safetySettings: fallbackSafetySettings,
        });

        return fallbackResult.response.text();
      } catch (fallbackError: any) {
        console.error('Fallback model also failed:', fallbackError);
        return `Error summarizing text: Unable to process with primary or fallback models. ${error.message}. Please try again or contact support.`;
      }
    }

    // For invalid argument issues
    if (error.message && error.message.includes('invalid argument')) {
      return `The text format may not be supported or contains invalid characters. Please try with different content.`;
    }

    // For quota exceeded issues
    if (error.message && (error.message.includes('quota') || error.message.includes('429'))) {
      return `API quota exceeded. The system is currently experiencing high demand. Please try again in a few minutes.`;
    }

    // Generic error
    return `Error summarizing text: ${error.message || 'Unknown error'}. Please try again or contact support.`;
  }
}

// New function to process PDF content with Gemini Vision
export async function processPdfWithVision(pdfBase64: string, customPrompt: string = DEFAULT_LEAD_GENERATION_PROMPT) {
  try {
    const genAI = initGemini();

    if (!genAI) {
      throw new Error("Gemini API not initialized");
    }

    // Use the model with increased quota for image/PDF processing
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-lite" });

    const generationConfig = {
      temperature: 0.4, // Lower temperature for more factual responses
      topK: 32,
      topP: 0.95,
      maxOutputTokens: 4096, // Increased for detailed analysis
    };

    const safetySettings = [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ];

    // Create image part from base64 data
    const imageParts = [{
      inlineData: {
        data: pdfBase64,
        mimeType: "application/pdf"
      }
    }];

    const result = await model.generateContent({
      contents: [{
        role: "user",
        parts: [
          { text: customPrompt },
          ...imageParts
        ]
      }],
      generationConfig,
      safetySettings,
    });

    const response = result.response;
    return response.text();

  } catch (error: any) {
    console.error('Error processing PDF with Gemini Vision:', error);

    // Check if it's a model-related error
    if (error.message && error.message.includes('model')) {
      // Try with a different model as fallback
      try {
        console.log('Attempting with fallback model gemini-1.5-flash...');
        const fallbackModel = genAI?.getGenerativeModel({ model: "gemini-2.5-flash" });

        if (!fallbackModel) {
          throw new Error("Fallback model initialization failed");
        }

        // Define fallback configuration
        const fallbackGenerationConfig = {
          temperature: 0.4,
          topK: 32,
          topP: 0.95,
          maxOutputTokens: 4096,
        };

        const fallbackSafetySettings = [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ];

        // Create image part from base64 data again for the fallback model
        const fallbackImageParts = [{
          inlineData: {
            data: pdfBase64,
            mimeType: "application/pdf"
          }
        }];

        const fallbackResult = await fallbackModel.generateContent({
          contents: [{
            role: "user",
            parts: [
              { text: customPrompt },
              ...fallbackImageParts
            ]
          }],
          generationConfig: fallbackGenerationConfig,
          safetySettings: fallbackSafetySettings,
        });

        return fallbackResult.response.text();
      } catch (fallbackError: any) {
        console.error('Fallback model also failed:', fallbackError);
        return `Error processing PDF: Unable to process with primary or fallback models. ${error.message}. Please try again or contact support.`;
      }
    }

    // For PDF format issues
    if (error.message && error.message.includes('invalid argument')) {
      return `The PDF format may not be supported or the file might be corrupted. Please try a different PDF or convert it to a different format.`;
    }

    // For quota exceeded issues
    if (error.message && (error.message.includes('quota') || error.message.includes('429'))) {
      return `API quota exceeded. The system is currently experiencing high demand. Please try again in a few minutes.`;
    }

    // Generic error
    return `Error processing PDF: ${error.message || 'Unknown error'}. Please try again or contact support.`;
  }
}