import { GoogleGenAI } from "@google/genai";

let genAI: GoogleGenAI | null = null;

export function initGemini() {
  if (!genAI) {
    // API key directly in the script as requested
    const API_KEY = "AIzaSyBVS_qKlCf1CUlURtb9MJ-TXiu44v0dR_4";
    console.log('Initializing Gemini with API key:', API_KEY ? 'Present' : 'Missing');
    genAI = new GoogleGenAI({ apiKey: API_KEY });
  }
  return genAI;
}

// Default lead generation prompt for UPS/stabilizer industry
export const DEFAULT_LEAD_GENERATION_PROMPT = `
🎯 PURPOSE
You are a lead-generation expert assistant for a company that manufactures UPS systems, Servo Stabilizers, Isolation Transformers, and Voltage Regulators.

You are given PDF files of epaper-style newspapers (e.g., Economic Times). These may be scanned or image-based PDFs.

Your job is to read the entire PDF using your vision capabilities, extract real business opportunities, and return structured results.

🔍 CAPABILITIES REQUIRED
✅ Use your own Vision model to read image-based PDF content (scanned newspaper layouts, photos with text, etc.)
✅ Use web search to find missing contact details (company websites, phone numbers, LinkedIn pages)
✅ Cover ALL pages of the PDF
✅ Output in a clean, sectioned, structured format

✅ WHAT TO EXTRACT AND RETURN:
1. 🏢 New Business Leads
Find any article announcing:

New factories / industrial plants

New schools, hospitals, colleges, malls, hotels, office complexes

Corporate expansions, infrastructure projects

Data centers, IT parks, SEZs

For each opportunity:

🏷 Name of company / organization

🏗 Project type (hospital, school, factory, etc.)

📍 Location (city/state)

🗓 Timeline (if mentioned)

📞 Contact info from PDF (if any)

🌐 If contact info NOT in PDF → Search the web and find:

Website

Phone

Email

LinkedIn of company/decision-maker

⚡ Use-Case Justification: "Why they may need UPS/Stabilizer"

2. ⚙ Stabilizer/UPS Use-Case Detection
For each lead above, clearly state:

How UPS or stabilizers would help

Link it to the sector (e.g., "Pharma plants need constant clean power to avoid batch failures")

3. 📈 Market Trends
Scan full newspaper and summarize:

📊 Industries on the rise (EV, Healthcare, Manufacturing, IT)

📉 Declining sectors (e.g., auto exports, textile closures)

🧾 Policy/govt news affecting infrastructure or energy demand

⚡ Mentions of energy issues, load shedding, carbon targets, solar/hybrid transitions

4. 🔍 Contact Discovery (Web + PDF)
For any entity mentioned in the articles:

Extract available phone numbers, emails, websites from the PDF

If not available, then:

Use real-time web search

Find company profile, contact page, LinkedIn profile of key people (e.g., VP Operations, MD)

5. 📄 Page Number References
Always include:

Page number where article was found

Source headline (if visible)


🆕 New Section to Include:
For every lead you extract, start with this new mandatory section:

📰 News Context Summary
Give a 2–4 line summary of the actual article content that explains:

What exactly is happening

Who's involved

Why it matters

Key triggers (investment amount, MOU signed, approvals, etc.)
`;

export async function summarizeText(text: string, customPrompt: string) {
  try {
    const genAI = initGemini();

    if (!genAI) {
      throw new Error("Gemini API not initialized");
    }

    // Combine the custom prompt with formatting instructions
    const prompt = `
    ${customPrompt}

    Article text:
    ${text}

    Format the response as:

    TITLE: [Summary Title]

    [Summary paragraphs]

    KEYWORDS: [List of important keywords or topics found in the text]
    `;

    const contents = [
      { text: prompt }
    ];

    const response = await genAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: contents
    });

    return response.text;

  } catch (error: any) {
    console.error('Error summarizing text with Gemini:', error);

    // Try with fallback model
    try {
      console.log('Attempting with fallback model gemini-1.5-flash...');

      const fallbackPrompt = `
      ${customPrompt}

      Article text:
      ${text}

      Format the response as:

      TITLE: [Summary Title]

      [Summary paragraphs]

      KEYWORDS: [List of important keywords or topics found in the text]
      `;

      const fallbackContents = [
        { text: fallbackPrompt }
      ];

      const fallbackResponse = await genAI!.models.generateContent({
        model: "gemini-1.5-flash",
        contents: fallbackContents
      });

      return fallbackResponse.text;
    } catch (fallbackError: any) {
      console.error('Fallback model also failed:', fallbackError);
      return `Error summarizing text: Unable to process with primary or fallback models. ${error.message}. Please try again or contact support.`;
    }
  }
}

// Function to process PDF content directly with Gemini
export async function processPdfAsText(pdfBase64: string, customPrompt: string = DEFAULT_LEAD_GENERATION_PROMPT) {
  try {
    const genAI = initGemini();

    if (!genAI) {
      throw new Error("Gemini API not initialized");
    }

    const contents = [
      { text: customPrompt },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: pdfBase64
        }
      }
    ];

    const response = await genAI.models.generateContent({
      model: "gemini-2.0-flash",
      contents: contents
    });

    return response.text;

  } catch (error: any) {
    console.error('Error processing PDF:', error);

    // Try with fallback model
    try {
      console.log('Attempting with fallback model gemini-1.5-flash...');

      const fallbackContents = [
        { text: customPrompt },
        {
          inlineData: {
            mimeType: 'application/pdf',
            data: pdfBase64
          }
        }
      ];

      const fallbackResponse = await genAI!.models.generateContent({
        model: "gemini-1.5-flash",
        contents: fallbackContents
      });

      return fallbackResponse.text;
    } catch (fallbackError: any) {
      console.error('Fallback model also failed:', fallbackError);
      return `Error processing PDF: Unable to process with primary or fallback models. ${error.message}. Please try again or contact support.`;
    }
  }
}

// Alternative function to process PDF with vision approach (same as processPdfAsText but with different model preference)
export async function processPdfWithVision(pdfBase64: string, customPrompt: string = DEFAULT_LEAD_GENERATION_PROMPT) {
  try {
    const genAI = initGemini();

    if (!genAI) {
      throw new Error("Gemini API not initialized");
    }

    const contents = [
      { text: customPrompt },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: pdfBase64
        }
      }
    ];

    // Try with gemini-1.5-pro first for vision tasks
    const response = await genAI.models.generateContent({
      model: "gemini-1.5-pro",
      contents: contents
    });

    return response.text;

  } catch (error: any) {
    console.error('Error processing PDF with Vision:', error);

    // Try with fallback model
    try {
      console.log('Attempting with fallback model gemini-2.0-flash...');

      const fallbackContents = [
        { text: customPrompt },
        {
          inlineData: {
            mimeType: 'application/pdf',
            data: pdfBase64
          }
        }
      ];

      const fallbackResponse = await genAI!.models.generateContent({
        model: "gemini-2.0-flash",
        contents: fallbackContents
      });

      return fallbackResponse.text;
    } catch (fallbackError: any) {
      console.error('Fallback model also failed:', fallbackError);

      // For PDF format issues
      if (error.message && error.message.includes('invalid argument')) {
        return `The PDF format may not be supported. Please try converting your PDF to images first, or extract the text and use the text input option.`;
      }

      // For quota exceeded issues
      if (error.message && (error.message.includes('quota') || error.message.includes('429'))) {
        return `API quota exceeded. Please try again in a few minutes.`;
      }

      return `Error processing PDF: Unable to process with primary or fallback models. ${error.message}. Please try again or contact support.`;
    }
  }
}