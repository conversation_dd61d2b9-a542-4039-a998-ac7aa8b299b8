import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ModeToggle } from "@/components/mode-toggle";
import { NewspaperIcon } from "@/components/icons/newspaper-icon";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b border-border/40">
        <div className="container flex h-16 items-center px-4 sm:px-6 lg:px-8">
          <div className="flex items-center gap-2">
            <NewspaperIcon className="h-8 w-8 text-primary" />
            <span className="font-serif text-2xl font-bold">NewsAI</span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <Link href="/login">
              <Button variant="ghost" size="sm">Log in</Button>
            </Link>
            <ModeToggle />
          </div>
        </div>
      </header>
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-serif font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                    AI-Powered Newspaper Summarization
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Extract targeted insights from newspaper articles with our keyword-based AI summarization technology.
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  <Link href="/login">
                    <Button size="lg" className="font-medium">
                      Get Started
                    </Button>
                  </Link>
                  <Link href="/login?tab=signup">
                    <Button size="lg" variant="outline" className="font-medium">
                      Sign Up
                    </Button>
                  </Link>
                </div>
                <div className="mt-6">
                  <p className="text-sm text-muted-foreground">
                    Powered by advanced AI technology
                  </p>
                </div>
              </div>
              <div className="relative hidden lg:block">
                <div className="absolute inset-0 bg-gradient-to-r from-background to-background/20" />
                <img
                  alt="Newspaper Summarization"
                  className="mx-auto aspect-video overflow-hidden rounded-xl object-cover"
                  src="https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
                />
              </div>
            </div>
          </div>
        </section>
        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-serif font-bold tracking-tighter sm:text-4xl">
                  How It Works
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl">
                  Our platform leverages advanced AI to extract concise summaries focused on the keywords that matter to you.
                </p>
              </div>
            </div>
            <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 lg:gap-12 mt-8">
              <div className="flex flex-col items-center space-y-2 rounded-lg p-4 card-hover">
                <div className="rounded-full bg-primary p-2 text-primary-foreground">
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                    <polyline points="14 2 14 8 20 8" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold">Upload Documents</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Upload your newspaper articles or copy-paste the text directly into our platform
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 rounded-lg p-4 card-hover">
                <div className="rounded-full bg-primary p-2 text-primary-foreground">
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="m21 21-6-6m6 6v-4.8m0 4.8h-4.8" />
                    <path d="M3 16.2V21m0-4.8H7.8" />
                    <path d="M21 7.8V3m0 4.8h-4.8" />
                    <path d="M3 7.8V3m0 4.8H7.8" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold">Select Keywords</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Specify the keywords that are most important for your analysis and focus areas
                </p>
              </div>
              <div className="flex flex-col items-center space-y-2 rounded-lg p-4 card-hover">
                <div className="rounded-full bg-primary p-2 text-primary-foreground">
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M2 12h10" />
                    <path d="M9 4v16" />
                    <path d="m3 9 3 3-3 3" />
                    <path d="M14 8h8" />
                    <path d="M18 4v16" />
                    <path d="m15 19 3-3 3 3" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold">Get AI Summaries</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Receive concise, targeted summaries that focus on your selected keywords
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
              <div className="space-y-2">
                <h2 className="text-3xl font-serif font-bold tracking-tighter sm:text-4xl">
                  Key Features
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl">
                  Our platform offers powerful features to help you extract the most relevant information from any newspaper article.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M20 5H9l-7 7 7 7h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Z" />
                    <line x1="18" x2="12" y1="9" y2="15" />
                    <line x1="12" x2="18" y1="9" y2="15" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">Smart Keyword Detection</h3>
                <p className="text-muted-foreground">
                  Our AI automatically identifies and highlights all instances of your keywords and related concepts in the text.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                    <polyline points="14 2 14 8 20 8" />
                    <path d="M16 13H8" />
                    <path d="M16 17H8" />
                    <path d="M10 9H8" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">Multiple File Formats</h3>
                <p className="text-muted-foreground">
                  Upload documents in various formats including PDF, DOC, DOCX, and plain text files.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M12 2v8" />
                    <path d="m4.93 10.93 1.41 1.41" />
                    <path d="M2 18h2" />
                    <path d="M20 18h2" />
                    <path d="m19.07 10.93-1.41 1.41" />
                    <path d="M22 22H2" />
                    <path d="m16 6-4 4-4-4" />
                    <path d="M16 18a4 4 0 0 0-8 0" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">One-Click Download</h3>
                <p className="text-muted-foreground">
                  Download your summaries in multiple formats for easy sharing and integration with other tools.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M3 3v18h18" />
                    <path d="m19 9-5 5-4-4-3 3" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">Analytics Dashboard</h3>
                <p className="text-muted-foreground">
                  Track your usage, view keyword frequency, and analyze trends across all your summarized documents.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                    <path d="m9 12 2 2 4-4" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">Secure Storage</h3>
                <p className="text-muted-foreground">
                  All your documents and summaries are securely stored and accessible from any device.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow card-hover">
                <div className="mb-4 text-primary">
                  <svg
                    className="h-10 w-10"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <path d="m4.93 4.93 14.14 14.14" />
                  </svg>
                </div>
                <h3 className="text-xl font-serif font-bold mb-2">Ad-Free Experience</h3>
                <p className="text-muted-foreground">
                  Enjoy a clean, distraction-free interface with no advertisements or pop-ups.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24 lg:py-32 bg-muted">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center mb-10">
              <div className="space-y-2">
                <h2 className="text-3xl font-serif font-bold tracking-tighter sm:text-4xl">
                  What Our Users Say
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl">
                  Trusted by journalists, researchers, and information professionals worldwide.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-card rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-primary/10 p-2 mr-4">
                    <svg
                      className="h-6 w-6 text-primary"
                      fill="none"
                      height="24"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold">Sarah Johnson</h4>
                    <p className="text-sm text-muted-foreground">Journalist, The Daily Chronicle</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "NewsAI has revolutionized my research process. I can quickly extract the most relevant information from multiple sources, saving me hours of work every day."
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-primary/10 p-2 mr-4">
                    <svg
                      className="h-6 w-6 text-primary"
                      fill="none"
                      height="24"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold">Michael Chen</h4>
                    <p className="text-sm text-muted-foreground">Research Analyst, Global Insights</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "The keyword-based summaries are incredibly accurate. The platform understands context and delivers precisely what I need without the fluff."
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-primary/10 p-2 mr-4">
                    <svg
                      className="h-6 w-6 text-primary"
                      fill="none"
                      height="24"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold">Emily Rodriguez</h4>
                    <p className="text-sm text-muted-foreground">Professor, Media Studies</p>
                  </div>
                </div>
                <p className="italic text-muted-foreground">
                  "I recommend NewsAI to all my students. It's an invaluable tool for media analysis and helps them focus on what matters in their research."
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-serif font-bold tracking-tighter sm:text-4xl">
                  Ready to Get Started?
                </h2>
                <p className="max-w-[900px] text-muted-foreground md:text-xl">
                  Join thousands of professionals who save time and improve their research with NewsAI.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 mt-8">
                <Link href="/login">
                  <Button size="lg" className="font-medium px-8">
                    Start Free Trial
                  </Button>
                </Link>
                <Link href="/login?tab=signup">
                  <Button size="lg" variant="outline" className="font-medium px-8">
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <footer className="border-t border-border/40">
        <div className="container flex flex-col gap-2 sm:flex-row py-6 px-4 items-center justify-between">
          <p className="text-xs text-muted-foreground">
            © 2025 NewsAI. All rights reserved.
          </p>
          <nav className="flex gap-4 sm:gap-6">
            <Link className="text-xs hover:underline underline-offset-4" href="#">
              Terms of Service
            </Link>
            <Link className="text-xs hover:underline underline-offset-4" href="#">
              Privacy
            </Link>
          </nav>
        </div>
      </footer>
    </div>
  );
}