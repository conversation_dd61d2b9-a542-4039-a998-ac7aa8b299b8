"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/lib/auth-provider";
import { db } from "@/lib/firebase";
import { collection, query, getDocs } from "firebase/firestore";
import { Summary, Document } from "@/types";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { FileText, Calendar, Clock, Tag, Download, Filter, Search, X, FileDown, FileType } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Document as DocxDocument, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, BorderStyle } from "docx";
import { saveAs } from "file-saver";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";

export default function DataHistoryPage() {
  const { userData } = useAuth();
  const [summaries, setSummaries] = useState<Summary[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("summaries");
  // Debug mode removed
  const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);
  const [documentDialogOpen, setDocumentDialogOpen] = useState(false);
  const [selectedSummary, setSelectedSummary] = useState<Summary | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [allKeywords, setAllKeywords] = useState<string[]>([]);

  // Function to export data in various formats
  const exportData = (format: 'json' | 'csv' | 'docx', filtered: boolean = false) => {
    // Get the data to export based on active tab and filter status
    let dataToExport = activeTab === "summaries" ? summaries : documents;

    // If filtered is true and we're on summaries tab, use the filtered data
    if (filtered && activeTab === "summaries") {
      dataToExport = getFilteredSummaries();
    }

    if (dataToExport.length === 0) {
      alert("No data to export");
      return;
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const dataType = activeTab === "summaries" ? "summaries" : "documents";
    const filterText = filtered ? "_filtered" : "";

    // Export based on format
    switch (format) {
      case 'json':
        exportAsJson(dataToExport, `${dataType}${filterText}_${timestamp}.json`);
        break;
      case 'csv':
        exportAsCsv(dataToExport, `${dataType}${filterText}_${timestamp}.csv`);
        break;
      case 'docx':
        exportAsDocx(dataToExport, `${dataType}${filterText}_${timestamp}.docx`);
        break;
    }
  };

  // Export as JSON
  const exportAsJson = (data: any[], filename: string) => {
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    saveAs(blob, filename);
  };

  // Export as CSV
  const exportAsCsv = (data: any[], filename: string) => {
    // Get all possible keys from all objects
    const allKeys = new Set<string>();
    data.forEach(item => {
      Object.keys(item).forEach(key => allKeys.add(key));
    });

    const headers = Array.from(allKeys);

    // Create CSV rows
    const rows = data.map(item => {
      return headers.map(header => {
        const value = item[header];

        // Handle different value types
        if (value === undefined || value === null) {
          return '""';
        } else if (Array.isArray(value)) {
          return `"${value.join('; ').replace(/"/g, '""')}"`;
        } else if (typeof value === 'object') {
          return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
        } else if (typeof value === 'string') {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return `"${value}"`;
      }).join(',');
    });

    // Combine headers and rows
    const csvContent = `${headers.join(',')}\n${rows.join('\n')}`;
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, filename);
  };

  // Export as DOCX
  const exportAsDocx = (data: any[], filename: string) => {
    const dataType = activeTab; // Get the current active tab
    const doc = new DocxDocument({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            text: `${dataType.charAt(0).toUpperCase() + dataType.slice(1)} Export`,
            heading: HeadingLevel.HEADING_1,
          }),
          new Paragraph({
            text: `Generated on ${format(new Date(), "MMMM d, yyyy 'at' h:mm a")}`,
            spacing: { after: 400 },
          }),
          ...generateDocxContentForType(data, dataType),
        ],
      }],
    });

    // Generate and save document
    Packer.toBlob(doc).then(blob => {
      saveAs(blob, filename);
    });
  };

  // Generate DOCX content based on explicit data type
  const generateDocxContentForType = (data: any[], dataType: string) => {
    const docxContent: Paragraph[] = [];

    // Process each item
    data.forEach((item, index) => {
      // Add separator between items
      if (index > 0) {
        docxContent.push(
          new Paragraph({
            text: "----------------------------------------",
            spacing: { before: 400, after: 400 },
          })
        );
      }

      // Add item title
      docxContent.push(
        new Paragraph({
          text: item.title || "Untitled",
          heading: HeadingLevel.HEADING_2,
          spacing: { after: 200 },
        })
      );

      // Add date information
      const dateField = dataType === "summaries" ? "createdAt" : "uploadDate";
      if (item[dateField]) {
        docxContent.push(
          new Paragraph({
            children: [
              new TextRun({ text: "Date: ", bold: true }),
              new TextRun(format(new Date(item[dateField]), "MMMM d, yyyy 'at' h:mm a")),
            ],
            spacing: { after: 200 },
          })
        );
      }

      // Add keywords if available
      if (item.keywords && item.keywords.length > 0) {
        docxContent.push(
          new Paragraph({
            children: [
              new TextRun({ text: "Keywords: ", bold: true }),
              new TextRun(item.keywords.join(", ")),
            ],
            spacing: { after: 200 },
          })
        );
      }

      // Add content based on data type
      if (dataType === "summaries" && item.content) {
        docxContent.push(
          new Paragraph({
            text: "Summary Content:",
            heading: HeadingLevel.HEADING_3,
            spacing: { after: 200 },
          }),
          new Paragraph({
            text: item.content,
            spacing: { after: 400 },
          })
        );
      } else if (dataType === "documents") {
        // Add file information
        if (item.fileName) {
          docxContent.push(
            new Paragraph({
              children: [
                new TextRun({ text: "File: ", bold: true }),
                new TextRun(`${item.fileName} (${(item.fileSize / 1024).toFixed(2)} KB)`),
              ],
              spacing: { after: 200 },
            })
          );
        }

        // Add original text if available
        if (item.originalText) {
          docxContent.push(
            new Paragraph({
              text: "Original Content:",
              heading: HeadingLevel.HEADING_3,
              spacing: { after: 200 },
            }),
            new Paragraph({
              text: item.originalText,
              spacing: { after: 400 },
            })
          );
        }
      }
    });

    return docxContent;
  };

  const fetchData = async () => {
    if (!userData) {
      console.log("No user data available, skipping data fetch");
      return;
    }

    console.log("Fetching data for user:", userData.uid);
    setLoading(true);
    try {
      // Debug code removed

      // Fetch summaries
      const summariesQuery = query(
        collection(db, "summaries")
        // Temporarily remove filters for debugging
        // where("userId", "==", userData.uid)
        // orderBy("createdAt", "desc")
      );
      console.log("Summaries query created");

      const summariesSnapshot = await getDocs(summariesQuery);
      console.log("Summaries snapshot received, count:", summariesSnapshot.docs.length);

      if (summariesSnapshot.docs.length > 0) {
        const summariesData = summariesSnapshot.docs.map(
          (doc: any) => {
            const data = doc.data();
            console.log("Summary data:", data);
            // Ensure all required fields are present
            return {
              id: data.id || doc.id,
              documentId: data.documentId || "",
              userId: data.userId || "",
              title: data.title || "Untitled Summary",
              content: data.content || "",
              keywords: Array.isArray(data.keywords) ? data.keywords : [],
              createdAt: data.createdAt || Date.now(),
            } as Summary;
          }
        );
        console.log("Processed summaries data:", summariesData);
        setSummaries(summariesData);

        // Extract all unique keywords
        const keywords = summariesData.flatMap((summary: Summary) => summary.keywords);
        const uniqueKeywords = Array.from(new Set(keywords)).sort() as string[];
        setAllKeywords(uniqueKeywords);
      } else {
        console.log("No summaries found");
        setSummaries([]);
      }

      // Fetch documents
      const documentsQuery = query(
        collection(db, "documents")
        // Temporarily remove filters for debugging
        // where("userId", "==", userData.uid)
        // orderBy("uploadDate", "desc")
      );
      console.log("Documents query created");

      const documentsSnapshot = await getDocs(documentsQuery);
      console.log("Documents snapshot received, count:", documentsSnapshot.docs.length);

      if (documentsSnapshot.docs.length > 0) {
        const documentsData = documentsSnapshot.docs.map(
          (doc: any) => {
            const data = doc.data();
            console.log("Document data:", data);
            // Ensure all required fields are present
            return {
              id: data.id || doc.id,
              userId: data.userId || "",
              title: data.title || "Untitled Document",
              originalText: data.originalText || "",
              fileUrl: data.fileUrl || "",
              fileName: data.fileName || "Unknown file",
              fileType: data.fileType || "text/plain",
              fileSize: data.fileSize || 0,
              uploadDate: data.uploadDate || Date.now(),
              keywords: Array.isArray(data.keywords) ? data.keywords : [],
              status: data.status || "completed",
            } as Document;
          }
        );
        console.log("Processed documents data:", documentsData);
        setDocuments(documentsData);
      } else {
        console.log("No documents found");
        setDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Function to filter summaries based on search term and selected keywords
  const getFilteredSummaries = useCallback(() => {
    return summaries.filter((summary) => {
      // Filter by search term
      const matchesSearch = searchTerm === "" ||
        summary.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        summary.content.toLowerCase().includes(searchTerm.toLowerCase());

      // Filter by selected keywords
      const matchesKeywords = selectedKeywords.length === 0 ||
        selectedKeywords.some(keyword => summary.keywords.includes(keyword));

      return matchesSearch && matchesKeywords;
    });
  }, [summaries, searchTerm, selectedKeywords]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchDataRef = useCallback(fetchData, [userData]);

  useEffect(() => {
    fetchDataRef();
  }, [fetchDataRef]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="mb-8">
          <Skeleton className="h-10 w-64 mb-2" />
          <Skeleton className="h-5 w-full max-w-2xl" />
        </div>
        <div className="grid gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-[200px] w-full rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-serif font-bold mb-2">Data & History</h1>
          <p className="text-muted-foreground">
            View your summarized documents and access your history
          </p>
        </div>
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <FileDown className="h-4 w-4 mr-2" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => exportData('json', false)}>
                Export All as JSON
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportData('csv', false)}>
                Export All as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportData('docx', false)}>
                Export All as DOCX
              </DropdownMenuItem>

              {activeTab === "summaries" && (searchTerm || selectedKeywords.length > 0) && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => exportData('json', true)}>
                    Export Filtered as JSON
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => exportData('csv', true)}>
                    Export Filtered as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => exportData('docx', true)}>
                    Export Filtered as DOCX
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            onClick={() => {
              console.log("Manual refresh");
              fetchData();
            }}
          >
            Refresh Data
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8 equal-tabs">
        <TabsList className="mb-6 grid w-full grid-cols-2">
          <TabsTrigger value="summaries">Summaries</TabsTrigger>
          <TabsTrigger value="documents">Original Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="summaries">
          {/* Filter UI */}
          <div className="mb-6 space-y-4">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search summaries..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowFilters(!showFilters)}
                className={showFilters ? "bg-muted" : ""}
              >
                <Filter className="h-4 w-4" />
                <span className="sr-only">Filter</span>
              </Button>
              {(searchTerm || selectedKeywords.length > 0) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedKeywords([]);
                  }}
                >
                  <X className="mr-2 h-4 w-4" />
                  Clear
                </Button>
              )}
            </div>

            {showFilters && (
              <div className="rounded-md border p-4 animate-in fade-in-50">
                <h3 className="font-medium mb-3">Filter by Keywords</h3>
                <div className="flex flex-wrap gap-2">
                  {allKeywords.map((keyword) => (
                    <Badge
                      key={keyword}
                      variant={selectedKeywords.includes(keyword) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        setSelectedKeywords((prev) =>
                          prev.includes(keyword)
                            ? prev.filter((k) => k !== keyword)
                            : [...prev, keyword]
                        );
                      }}
                    >
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {getFilteredSummaries().length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              {summaries.length === 0 ? (
                <>
                  <h3 className="text-xl font-medium mb-2">No summaries yet</h3>
                  <p className="text-muted-foreground mb-6">
                    Upload a document to generate your first summary
                  </p>
                  <Button onClick={() => window.location.href = "/dashboard"}>
                    Create Summary
                  </Button>
                </>
              ) : (
                <>
                  <h3 className="text-xl font-medium mb-2">No matching summaries</h3>
                  <p className="text-muted-foreground mb-6">
                    Try adjusting your search or filters
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedKeywords([]);
                    }}
                  >
                    Clear Filters
                  </Button>
                </>
              )}
            </div>
          ) : (
            <>
              {(searchTerm || selectedKeywords.length > 0) && (
                <div className="mb-4 text-sm text-muted-foreground">
                  Showing {getFilteredSummaries().length} of {summaries.length} summaries
                </div>
              )}
              <div className="grid gap-6">
                {getFilteredSummaries().map((summary) => (
                <Card key={summary.id} className="overflow-hidden">
                  <CardHeader>
                    <CardTitle>{summary.title}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {format(new Date(summary.createdAt), "MMMM d, yyyy")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2 mb-4">
                        {summary.keywords.map((keyword, i) => (
                          <Badge key={i} variant="secondary">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-muted-foreground whitespace-pre-line">
                        {summary.content.length > 300
                          ? `${summary.content.substring(0, 300)}...`
                          : summary.content}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t bg-muted/50 px-6 py-3">
                    <div className="text-xs text-muted-foreground flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {format(new Date(summary.createdAt), "h:mm a")}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedSummary(summary);
                        setSummaryDialogOpen(true);
                      }}
                    >
                      View Full Summary
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="documents">
          {documents.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium mb-2">No documents yet</h3>
              <p className="text-muted-foreground mb-6">
                Upload a document to get started
              </p>
              <Button onClick={() => window.location.href = "/dashboard"}>
                Upload Document
              </Button>
            </div>
          ) : (
            <div className="grid gap-6">
              {documents.map((document) => (
                <Card key={document.id}>
                  <CardHeader>
                    <CardTitle>{document.title}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {format(new Date(document.uploadDate), "MMMM d, yyyy")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center text-sm">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{document.fileName}</span>
                        <span className="ml-2 text-muted-foreground">
                          ({(document.fileSize / 1024).toFixed(2)} KB)
                        </span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
                        <div className="flex flex-wrap gap-2">
                          {document.keywords.map((keyword, i) => (
                            <Badge key={i} variant="outline">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t bg-muted/50 px-6 py-3">
                    <div className="text-xs text-muted-foreground flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {format(new Date(document.uploadDate), "h:mm a")}
                    </div>
                    <div className="flex gap-2">
                      {document.fileUrl && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={document.fileUrl} target="_blank" rel="noopener noreferrer">
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </a>
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedDocument(document);
                          setDocumentDialogOpen(true);
                        }}
                      >
                        View Details
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Summary Dialog */}
      <Dialog open={summaryDialogOpen} onOpenChange={setSummaryDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          {selectedSummary && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-serif">{selectedSummary.title}</DialogTitle>
                <DialogDescription className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {format(new Date(selectedSummary.createdAt), "MMMM d, yyyy")}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {format(new Date(selectedSummary.createdAt), "h:mm a")}
                  </div>
                </DialogDescription>
              </DialogHeader>

              <div className="my-4">
                <div className="mb-4">
                  <div className="text-xs text-muted-foreground mb-2">
                    Summary ID: {selectedSummary.id}
                    {selectedSummary.documentId && (
                      <span className="ml-2">| Document ID: {selectedSummary.documentId}</span>
                    )}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedSummary.keywords.map((keyword, i) => (
                      <Badge key={i} variant="secondary">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div className="whitespace-pre-line bg-muted/50 p-4 rounded-md">{selectedSummary.content}</div>
                </div>
              </div>

              <DialogFooter className="flex justify-between">
                <div className="flex gap-2">
                  {selectedSummary.documentId && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Find the related document
                        const relatedDoc = documents.find(doc => doc.id === selectedSummary.documentId);
                        if (relatedDoc) {
                          setSummaryDialogOpen(false);
                          setSelectedDocument(relatedDoc);
                          setTimeout(() => {
                            setDocumentDialogOpen(true);
                          }, 100);
                        }
                      }}
                    >
                      View Original Document
                    </Button>
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <FileDown className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => {
                        // Export as JSON
                        exportAsJson([selectedSummary], `summary_${selectedSummary.id}_${new Date().toISOString().split('T')[0]}.json`);
                      }}>
                        Export as JSON
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Export as CSV
                        exportAsCsv([selectedSummary], `summary_${selectedSummary.id}_${new Date().toISOString().split('T')[0]}.csv`);
                      }}>
                        Export as CSV
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Create a temporary copy of the data with the correct tab type
                        const tempData = [...[selectedSummary]];
                        // Export as DOCX with "summaries" as the type
                        const doc = new DocxDocument({
                          sections: [{
                            properties: {},
                            children: [
                              new Paragraph({
                                text: `Summary Export`,
                                heading: HeadingLevel.HEADING_1,
                              }),
                              new Paragraph({
                                text: `Generated on ${format(new Date(), "MMMM d, yyyy 'at' h:mm a")}`,
                                spacing: { after: 400 },
                              }),
                              ...generateDocxContentForType(tempData, "summaries"),
                            ],
                          }],
                        });

                        // Generate and save document
                        Packer.toBlob(doc).then(blob => {
                          saveAs(blob, `summary_${selectedSummary.id}_${new Date().toISOString().split('T')[0]}.docx`);
                        });
                      }}>
                        Export as DOCX
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <DialogClose asChild>
                  <Button variant="outline">Close</Button>
                </DialogClose>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Document Dialog */}
      <Dialog open={documentDialogOpen} onOpenChange={setDocumentDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          {selectedDocument && (
            <>
              <DialogHeader>
                <DialogTitle className="text-2xl font-serif">{selectedDocument.title}</DialogTitle>
                <DialogDescription className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {format(new Date(selectedDocument.uploadDate), "MMMM d, yyyy")}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {format(new Date(selectedDocument.uploadDate), "h:mm a")}
                  </div>
                </DialogDescription>
              </DialogHeader>

              <div className="my-4">
                <div className="flex flex-col gap-4">
                  <div className="text-xs text-muted-foreground mb-2">
                    Document ID: {selectedDocument.id}
                  </div>

                  <div className="flex items-center text-sm">
                    <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="font-medium">File:</span>
                    <span className="ml-2">{selectedDocument.fileName}</span>
                    <span className="ml-2 text-muted-foreground">
                      ({(selectedDocument.fileSize / 1024).toFixed(2)} KB)
                    </span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="font-medium">Keywords:</span>
                    <div className="flex flex-wrap gap-2 ml-2">
                      {selectedDocument.keywords.map((keyword, i) => (
                        <Badge key={i} variant="outline">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-2">Original Content</h3>
                  <div className="bg-muted p-4 rounded-md overflow-auto max-h-[300px] whitespace-pre-line">
                    {selectedDocument.originalText}
                  </div>
                </div>
              </div>

              <DialogFooter className="flex justify-between">
                <div className="flex gap-2">
                  {selectedDocument.fileUrl && (
                    <Button variant="outline" asChild>
                      <a href={selectedDocument.fileUrl} target="_blank" rel="noopener noreferrer">
                        <Download className="h-4 w-4 mr-1" />
                        Download Original
                      </a>
                    </Button>
                  )}

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <FileDown className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => {
                        // Export as JSON
                        exportAsJson([selectedDocument], `document_${selectedDocument.id}_${new Date().toISOString().split('T')[0]}.json`);
                      }}>
                        Export as JSON
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Export as CSV
                        exportAsCsv([selectedDocument], `document_${selectedDocument.id}_${new Date().toISOString().split('T')[0]}.csv`);
                      }}>
                        Export as CSV
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => {
                        // Create a temporary copy of the data with the correct tab type
                        const tempData = [...[selectedDocument]];
                        // Export as DOCX with "documents" as the type
                        const doc = new DocxDocument({
                          sections: [{
                            properties: {},
                            children: [
                              new Paragraph({
                                text: `Document Export`,
                                heading: HeadingLevel.HEADING_1,
                              }),
                              new Paragraph({
                                text: `Generated on ${format(new Date(), "MMMM d, yyyy 'at' h:mm a")}`,
                                spacing: { after: 400 },
                              }),
                              ...generateDocxContentForType(tempData, "documents"),
                            ],
                          }],
                        });

                        // Generate and save document
                        Packer.toBlob(doc).then(blob => {
                          saveAs(blob, `document_${selectedDocument.id}_${new Date().toISOString().split('T')[0]}.docx`);
                        });
                      }}>
                        Export as DOCX
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Find related summaries */}
                  {summaries.some(s => s.documentId === selectedDocument.id) && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Find the related summary
                        const relatedSummary = summaries.find(s => s.documentId === selectedDocument.id);
                        if (relatedSummary) {
                          setDocumentDialogOpen(false);
                          setSelectedSummary(relatedSummary);
                          setTimeout(() => {
                            setSummaryDialogOpen(true);
                          }, 100);
                        }
                      }}
                    >
                      View Summary
                    </Button>
                  )}
                </div>
                <DialogClose asChild>
                  <Button variant="outline">Close</Button>
                </DialogClose>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
